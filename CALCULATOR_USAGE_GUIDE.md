# Cloud Cost Calculator - Usage Guide

## Quick Start

### 1. Seed the Database
```bash
# Run the seeding script to populate calculator data
node database/migrations/run-seed.js
```

### 2. API Endpoints

#### Get Calculator Configuration
```http
GET /api/cloud-cost-calculator
```

#### Calculate Cost
```http
POST /api/cloud-cost-calculator/calculate
Content-Type: application/json

{
  "answers": {
    "server_count": "50_to_100",
    "server_capacity": "10_50_tb",
    "high_availability": "yes",
    "environments": ["dev", "staging", "production"],
    "compliance_requirements": ["gdpr", "soc2"],
    "migration_strategy": "re_platforming",
    "auto_scaling": "yes"
  }
}
```

#### Submit Calculator Results
```http
POST /api/calculator-submissions
Content-Type: application/json

{
  "data": {
    "user_answers": { /* user responses */ },
    "calculated_cost": 525000,
    "cost_breakdown": { /* breakdown object */ },
    "email": "<EMAIL>",
    "company_name": "Example Corp",
    "contact_name": "<PERSON>",
    "follow_up_requested": true,
    "consultation_requested": true
  }
}
```

## Frontend Integration Examples

### React/Next.js Integration

```jsx
import { useState, useEffect } from 'react';

const CloudCostCalculator = () => {
  const [calculator, setCalculator] = useState(null);
  const [currentSection, setCurrentSection] = useState(0);
  const [answers, setAnswers] = useState({});
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Fetch calculator configuration
    fetch('/api/cloud-cost-calculator')
      .then(res => res.json())
      .then(data => setCalculator(data.data));
  }, []);

  const handleAnswerChange = (questionId, value) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }));
  };

  const calculateCost = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/cloud-cost-calculator/calculate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ answers })
      });
      const result = await response.json();
      setResult(result.data);
    } catch (error) {
      console.error('Calculation error:', error);
    } finally {
      setLoading(false);
    }
  };

  const submitResults = async (contactInfo) => {
    try {
      await fetch('/api/calculator-submissions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          data: {
            user_answers: answers,
            calculated_cost: result.totalCost,
            cost_breakdown: result.costBreakdown,
            ...contactInfo
          }
        })
      });
    } catch (error) {
      console.error('Submission error:', error);
    }
  };

  if (!calculator) return <div>Loading...</div>;

  return (
    <div className="calculator">
      {/* Render calculator sections and questions */}
      {result ? (
        <ResultsDisplay result={result} onSubmit={submitResults} />
      ) : (
        <QuestionForm 
          calculator={calculator}
          answers={answers}
          onAnswerChange={handleAnswerChange}
          onCalculate={calculateCost}
          loading={loading}
        />
      )}
    </div>
  );
};
```

### Vue.js Integration

```vue
<template>
  <div class="calculator">
    <div v-if="!result" class="questions">
      <div v-for="section in calculator?.calculator_sections" :key="section.id">
        <h2>{{ section.title }}</h2>
        <div v-for="question in section.questions" :key="question.id">
          <label>{{ question.question_text }}</label>
          <div v-if="question.question_type === 'single_choice'">
            <input 
              v-for="option in question.options" 
              :key="option.id"
              type="radio" 
              :name="question.question_identifier"
              :value="option.value"
              @change="updateAnswer(question.question_identifier, option.value)"
            />
            <label>{{ option.label }}</label>
          </div>
        </div>
      </div>
      <button @click="calculateCost" :disabled="loading">
        {{ loading ? 'Calculating...' : 'Calculate Cost' }}
      </button>
    </div>
    
    <div v-else class="results">
      <h2>Your Estimated Cost: ${{ result.totalCost.toLocaleString() }}</h2>
      <div class="cost-breakdown">
        <div v-for="(amount, category) in result.costBreakdown" :key="category">
          {{ category }}: ${{ amount.toLocaleString() }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      calculator: null,
      answers: {},
      result: null,
      loading: false
    };
  },
  async mounted() {
    const response = await fetch('/api/cloud-cost-calculator');
    this.calculator = (await response.json()).data;
  },
  methods: {
    updateAnswer(questionId, value) {
      this.answers[questionId] = value;
    },
    async calculateCost() {
      this.loading = true;
      try {
        const response = await fetch('/api/cloud-cost-calculator/calculate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ answers: this.answers })
        });
        this.result = (await response.json()).data;
      } catch (error) {
        console.error('Calculation error:', error);
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>
```

## Cost Calculation Logic

### Example Calculation Flow

1. **Base Infrastructure Cost**: Based on server count (Question 2)
   - 50-100 servers = $250,000

2. **Data Migration Cost**: Based on capacity (Question 5)
   - 10-50 TB = $50,000

3. **Environment Costs**: Multiple selection (Question 13)
   - Development = $10,000
   - Staging = $20,000
   - Production = $100,000
   - Total = $130,000

4. **Compliance Costs**: Multiple selection (Question 14)
   - GDPR = $10,000
   - SOC 2 = $15,000
   - Total = $25,000

5. **Migration Strategy**: Single selection (Question 15)
   - Re-platforming = $50,000

6. **Auto-scaling**: Single selection (Question 16)
   - Yes = $10,000

7. **High Availability**: Percentage-based (Question 10)
   - Yes = +20% of (Infrastructure + Data Migration)
   - 20% of ($250,000 + $50,000) = $60,000

**Total Calculation**:
- Base: $250,000 + $50,000 + $130,000 + $25,000 + $50,000 + $10,000 = $515,000
- High Availability: +$60,000
- **Final Total**: $575,000
- **Upper Range** (30% buffer): $747,500

## Analytics & Reporting

### Get Analytics Data
```http
GET /api/calculator-submissions/analytics?startDate=2024-01-01&endDate=2024-12-31&groupBy=month
```

### Response Example
```json
{
  "data": {
    "summary": {
      "totalSubmissions": 150,
      "averageCost": 425000,
      "consultationRequests": 45,
      "conversionRate": 30.0
    },
    "timeline": [
      {
        "period": "2024-01",
        "submissions": 12,
        "totalCost": 5100000,
        "consultations": 4
      }
    ],
    "costDistribution": [
      { "label": "Under $100K", "count": 15 },
      { "label": "$100K - $500K", "count": 85 },
      { "label": "$500K - $1M", "count": 35 },
      { "label": "Over $1M", "count": 15 }
    ],
    "industryBreakdown": [
      { "industry": "Technology", "count": 45, "percentage": 30.0 },
      { "industry": "Healthcare", "count": 30, "percentage": 20.0 }
    ]
  }
}
```

## Customization

### Adding New Questions
1. Create question in Strapi admin
2. Set question type and options
3. Configure pricing rules
4. Update frontend to handle new question type

### Modifying Pricing Logic
1. Update pricing rules in calculator configuration
2. Modify service calculation logic if needed
3. Test with various answer combinations

### Custom Recommendations
1. Add recommendation components
2. Set conditions for when to show
3. Configure priority and messaging

## Best Practices

1. **Validation**: Always validate user inputs
2. **Error Handling**: Provide clear error messages
3. **Performance**: Cache calculator configuration
4. **Analytics**: Track user behavior and drop-off points
5. **Testing**: Test all pricing scenarios thoroughly
6. **Security**: Sanitize all inputs and validate on server-side
