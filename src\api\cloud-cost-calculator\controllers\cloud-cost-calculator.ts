/**
 * cloud-cost-calculator controller
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::cloud-cost-calculator.cloud-cost-calculator', ({ strapi }) => ({
  async find(ctx) {
    const { data, meta } = await super.find(ctx);
    return { data, meta };
  },

  async calculateCost(ctx) {
    try {
      const { answers } = ctx.request.body;
      
      if (!answers || typeof answers !== 'object') {
        return ctx.badRequest('Invalid answers format');
      }

      // Get calculator configuration
      const calculator = await strapi.entityService.findMany('api::cloud-cost-calculator.cloud-cost-calculator', {
        populate: {
          calculator_sections: {
            populate: {
              questions: {
                populate: {
                  options: true,
                  pricing_rules: true
                }
              }
            }
          },
          pricing_rules: true,
          result_configuration: true
        }
      });

      if (!calculator) {
        return ctx.notFound('Calculator configuration not found');
      }

      // Calculate cost based on answers
      const costCalculation = await strapi.service('api::cloud-cost-calculator.cloud-cost-calculator').calculateCost(answers, calculator);

      return {
        data: {
          totalCost: costCalculation.totalCost,
          costBreakdown: costCalculation.breakdown,
          recommendations: costCalculation.recommendations,
          upperRange: Math.round(costCalculation.totalCost * 1.3), // 30% upper range as per sheet
          lowerRange: costCalculation.totalCost
        }
      };
    } catch (error) {
      strapi.log.error('Cost calculation error:', error);
      return ctx.internalServerError('Error calculating cost');
    }
  }
}));
