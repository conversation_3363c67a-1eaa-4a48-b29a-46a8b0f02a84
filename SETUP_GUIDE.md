# Cloud Cost Calculator - Setup Guide

## 🚀 Quick Start

### 1. Start Strapi Development Server
```bash
npm run develop
# or
yarn develop
```

### 2. Access Strapi Admin
- Open: http://localhost:1337/admin
- Create admin account if first time
- Login to admin panel

### 3. Seed Calculator Data
```bash
# Make sure <PERSON><PERSON><PERSON> is running first, then:
node scripts/seed-calculator.js
```

## 📋 Manual Setup (Alternative)

If the seeding script doesn't work, you can manually create the content in Strapi admin:

### Step 1: Create Calculator Sections
Go to **Content Manager > Calculator Section** and create:

1. **Business & Infrastructure Assessment**
   - Section Number: 1
   - Section Identifier: `business_infrastructure`
   - Description: "Assess your current infrastructure and migration scope"

2. **Workload & Resource Analysis**
   - Section Number: 2
   - Section Identifier: `workload_analysis`
   - Description: "Analyze your workloads and resource requirements"

3. **Cloud Provider & Deployment Preferences**
   - Section Number: 3
   - Section Identifier: `cloud_deployment`
   - Description: "Choose your cloud provider and deployment strategy"

4. **Security, Compliance & Migration Strategy**
   - Section Number: 4
   - Section Identifier: `security_compliance`
   - Description: "Define security requirements and migration approach"

5. **Post-Migration & Optimization**
   - Section Number: 5
   - Section Identifier: `post_migration`
   - Description: "Plan for post-migration optimization and management"

### Step 2: Create Calculator Questions

#### Question 1: Server Count (Section 1)
- **Question Text**: "Approximately how many servers do you intend to migrate?"
- **Question Type**: single_choice
- **Question Identifier**: `server_count`
- **Question Number**: 1
- **Options** (add in Components):
  - "< 10 servers" (value: `under_10`, cost: 10000)
  - "10 – 50" (value: `10_to_50`, cost: 50000)
  - "50 – 100" (value: `50_to_100`, cost: 250000)
  - "> 100" (value: `over_100`, cost: 500000)

#### Question 2: Server Capacity (Section 1)
- **Question Text**: "What is the total capacity of your servers?"
- **Question Type**: single_choice
- **Question Identifier**: `server_capacity`
- **Question Number**: 2
- **Options**:
  - "10 GB – 50 GB" (value: `10_50_gb`, cost: 100)
  - "50 GB – 200 GB" (value: `50_200_gb`, cost: 500)
  - "200 GB – 1 TB" (value: `200gb_1tb`, cost: 2000)
  - "1 TB – 10 TB" (value: `1_10_tb`, cost: 10000)
  - "10 TB – 50 TB" (value: `10_50_tb`, cost: 50000)
  - "> 50 TB" (value: `over_50_tb`, cost: 150000)

#### Question 3: High Availability (Section 2)
- **Question Text**: "Do you require high availability or disaster recovery for critical applications?"
- **Question Type**: single_choice
- **Question Identifier**: `high_availability`
- **Question Number**: 3
- **Options**:
  - "Yes" (value: `yes`, cost: 0, percentage: 20)
  - "No" (value: `no`, cost: 0)

### Step 3: Create Main Calculator Configuration
Go to **Content Manager > Cloud Cost Calculator** and create:

- **Hero Section**:
  - Title: "Cloud Migration Cost Calculator"
  - Description: "Get an accurate estimate for your cloud migration project based on your specific requirements and infrastructure."

- **Disclaimer**: "This estimate is based on typical migration scenarios and may vary based on specific requirements, complexity, and timeline. Contact our team for a detailed assessment."

## 🧪 Testing

### Test API Endpoints
```bash
# Test the calculator configuration
curl http://localhost:1337/api/cloud-cost-calculator

# Test cost calculation
curl -X POST http://localhost:1337/api/cloud-cost-calculator/calculate \
  -H "Content-Type: application/json" \
  -d '{"answers": {"server_count": "50_to_100", "server_capacity": "10_50_tb", "high_availability": "yes"}}'
```

### Run Test Script
```bash
node scripts/test-calculator-api.js
```

## 🔧 Configuration

### Environment Variables
Create `.env` file with:
```env
# Optional: For authenticated API requests
STRAPI_API_TOKEN=your_api_token_here

# Optional: For email notifications
SALES_EMAIL=<EMAIL>
ADMIN_URL=http://localhost:1337
```

### Pricing Configuration
1. Go to **Content Manager > Calculator Question**
2. Edit each question to add options with pricing
3. Configure percentage-based pricing for high availability
4. Set up conditional pricing rules as needed

## 📊 Expected Results

### Sample Calculation:
**Input:**
- 50-100 servers
- 10-50TB capacity  
- High availability: Yes

**Expected Output:**
- Infrastructure: $250,000
- Data Migration: $50,000
- High Availability: +$60,000 (20% of $300K)
- **Total: $360,000**
- **Upper Range: $468,000** (30% buffer)

## 🎯 Next Steps

1. **Complete Question Setup**: Add all 17 questions from the Google Sheets
2. **Configure Pricing Rules**: Set up complex pricing logic
3. **Frontend Integration**: Build the calculator UI
4. **Testing**: Verify all calculations match expected results
5. **Deployment**: Deploy to production environment

## 🆘 Troubleshooting

### Common Issues:

1. **Migration Error**: Remove migration files and use manual seeding
2. **API Errors**: Check Strapi is running and content types are created
3. **Pricing Issues**: Verify question options have correct cost values
4. **TypeScript Errors**: All should be resolved, restart if needed

### Getting Help:
- Check Strapi documentation: https://docs.strapi.io/
- Review the implementation files in this project
- Test with the provided scripts
