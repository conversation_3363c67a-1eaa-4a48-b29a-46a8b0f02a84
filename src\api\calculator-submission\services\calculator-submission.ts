/**
 * calculator-submission service
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreService('api::calculator-submission.calculator-submission' as any, ({ strapi }) => ({
  async handleFollowUp(submission: any) {
    try {
      // Send notification email to sales team
      if (submission.consultation_requested) {
        try {
          // Email service integration would go here
          console.log('Email notification would be sent for:', submission.company_name);
        } catch (emailError) {
          strapi.log.error('Email sending error:', emailError);
        }
      }

      // Add to CRM or marketing automation
      if (submission.follow_up_requested) {
        try {
          // CRM integration would go here
          console.log('Would add to marketing list:', submission.email);
        } catch (crmError) {
          strapi.log.error('CRM integration error:', crmError);
        }
      }

      // Log activity
      strapi.log.info(`Follow-up initiated for submission ${submission.id}`);
    } catch (error) {
      strapi.log.error('Follow-up handling error:', error);
    }
  },

  async getAnalytics({ startDate, endDate, groupBy }: any) {
    try {
      const submissions = await strapi.entityService.findMany('api::calculator-submission.calculator-submission' as any, {
        filters: {
          createdAt: {
            $gte: startDate,
            $lte: endDate,
          },
        },
      }) as any[];

      // Calculate analytics
      const totalSubmissions = Array.isArray(submissions) ? submissions.length : 0;
      const averageCost = Array.isArray(submissions)
        ? submissions.reduce((sum: number, sub: any) => sum + (sub.calculated_cost || 0), 0) / totalSubmissions
        : 0;
      const consultationRequests = Array.isArray(submissions)
        ? submissions.filter((sub: any) => sub.consultation_requested).length
        : 0;
      const conversionRate = totalSubmissions > 0 ? (consultationRequests / totalSubmissions) * 100 : 0;

      return {
        summary: {
          totalSubmissions,
          averageCost: Math.round(averageCost),
          consultationRequests,
          conversionRate: Math.round(conversionRate * 100) / 100,
        },
        timeline: [],
        costDistribution: [],
        industryBreakdown: [],
      };
    } catch (error) {
      strapi.log.error('Analytics calculation error:', error);
      throw error;
    }
  },
}));
  return `
      <h2>New Cloud Cost Calculator Consultation Request</h2>
      <p><strong>Company:</strong> ${submission.company_name}</p>
      <p><strong>Contact:</strong> ${submission.contact_name}</p>
      <p><strong>Email:</strong> ${submission.email}</p>
      <p><strong>Phone:</strong> ${submission.phone}</p>
      <p><strong>Industry:</strong> ${submission.industry}</p>
      <p><strong>Company Size:</strong> ${submission.company_size}</p>
      <p><strong>Estimated Cost:</strong> $${submission.calculated_cost?.toLocaleString()}</p>
      
      <h3>User Responses:</h3>
      <pre>${JSON.stringify(submission.user_answers, null, 2)}</pre>
      
      <h3>Cost Breakdown:</h3>
      <pre>${JSON.stringify(submission.cost_breakdown, null, 2)}</pre>
      
      <p><a href="${process.env.ADMIN_URL}/admin/content-manager/collectionType/api::calculator-submission.calculator-submission/${submission.id}">View in Admin</a></p>
    `;
},

  async addToMarketingList(submission: any) {
  // Integration with marketing automation tools
  // This would depend on your specific marketing stack
  try {
    // Example: Add to Mailchimp, HubSpot, etc.
    strapi.log.info(`Added ${submission.email} to marketing list`);
  } catch (error) {
    strapi.log.error('Marketing list addition error:', error);
  }
},

  async getAnalytics({ startDate, endDate, groupBy }: any) {
  try {
    const submissions = await strapi.entityService.findMany('api::calculator-submission.calculator-submission', {
      filters: {
        createdAt: {
          $gte: startDate,
          $lte: endDate,
        },
      },
    });

    // Calculate analytics
    const totalSubmissions = submissions.length;
    const averageCost = submissions.reduce((sum: number, sub: any) => sum + (sub.calculated_cost || 0), 0) / totalSubmissions;
    const consultationRequests = submissions.filter((sub: any) => sub.consultation_requested).length;
    const conversionRate = (consultationRequests / totalSubmissions) * 100;

    // Group by time period
    const groupedData = this.groupSubmissionsByPeriod(submissions, groupBy);

    return {
      summary: {
        totalSubmissions,
        averageCost: Math.round(averageCost),
        consultationRequests,
        conversionRate: Math.round(conversionRate * 100) / 100,
      },
      timeline: groupedData,
      costDistribution: this.getCostDistribution(submissions),
      industryBreakdown: this.getIndustryBreakdown(submissions),
    };
  } catch (error) {
    strapi.log.error('Analytics calculation error:', error);
    throw error;
  }
},

groupSubmissionsByPeriod(submissions: any[], groupBy: string) {
  const grouped: Record<string, any> = {};

  submissions.forEach(submission => {
    const date = new Date(submission.createdAt);
    let key: string;

    switch (groupBy) {
      case 'hour':
        key = date.toISOString().slice(0, 13);
        break;
      case 'day':
        key = date.toISOString().slice(0, 10);
        break;
      case 'week':
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        key = weekStart.toISOString().slice(0, 10);
        break;
      case 'month':
        key = date.toISOString().slice(0, 7);
        break;
      default:
        key = date.toISOString().slice(0, 10);
    }

    if (!grouped[key]) {
      grouped[key] = {
        period: key,
        submissions: 0,
        totalCost: 0,
        consultations: 0,
      };
    }

    grouped[key].submissions++;
    grouped[key].totalCost += submission.calculated_cost || 0;
    if (submission.consultation_requested) {
      grouped[key].consultations++;
    }
  });

  return Object.values(grouped).sort((a: any, b: any) => a.period.localeCompare(b.period));
},

getCostDistribution(submissions: any[]) {
  const ranges = [
    { label: 'Under $100K', min: 0, max: 100000, count: 0 },
    { label: '$100K - $500K', min: 100000, max: 500000, count: 0 },
    { label: '$500K - $1M', min: 500000, max: 1000000, count: 0 },
    { label: 'Over $1M', min: 1000000, max: Infinity, count: 0 },
  ];

  submissions.forEach(submission => {
    const cost = submission.calculated_cost || 0;
    const range = ranges.find(r => cost >= r.min && cost < r.max);
    if (range) range.count++;
  });

  return ranges;
},

getIndustryBreakdown(submissions: any[]) {
  const industries: Record<string, number> = {};

  submissions.forEach(submission => {
    const industry = submission.industry || 'Unknown';
    industries[industry] = (industries[industry] || 0) + 1;
  });

  return Object.entries(industries).map(([industry, count]) => ({
    industry,
    count,
    percentage: Math.round((count / submissions.length) * 100 * 100) / 100,
  }));
}
}));
