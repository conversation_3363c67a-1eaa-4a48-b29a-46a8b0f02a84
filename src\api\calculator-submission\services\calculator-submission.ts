/**
 * calculator-submission service
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreService('api::calculator-submission.calculator-submission' as any, ({ strapi }) => ({
  async handleFollowUp(submission: any) {
    try {
      // Send notification email to sales team
      if (submission.consultation_requested) {
        try {
          // Email service integration would go here
          console.log('Email notification would be sent for:', submission.company_name);
        } catch (emailError) {
          strapi.log.error('Email sending error:', emailError);
        }
      }

      // Add to CRM or marketing automation
      if (submission.follow_up_requested) {
        try {
          // CRM integration would go here
          console.log('Would add to marketing list:', submission.email);
        } catch (crmError) {
          strapi.log.error('CRM integration error:', crmError);
        }
      }

      // Log activity
      strapi.log.info(`Follow-up initiated for submission ${submission.id}`);
    } catch (error) {
      strapi.log.error('Follow-up handling error:', error);
    }
  },

  async getAnalytics({ startDate, endDate, groupBy }: any) {
    try {
      const submissions = await strapi.entityService.findMany('api::calculator-submission.calculator-submission' as any, {
        filters: {
          createdAt: {
            $gte: startDate,
            $lte: endDate,
          },
        },
      }) as any[];

      // Calculate analytics
      const totalSubmissions = Array.isArray(submissions) ? submissions.length : 0;
      const averageCost = Array.isArray(submissions)
        ? submissions.reduce((sum: number, sub: any) => sum + (sub.calculated_cost || 0), 0) / totalSubmissions
        : 0;
      const consultationRequests = Array.isArray(submissions)
        ? submissions.filter((sub: any) => sub.consultation_requested).length
        : 0;
      const conversionRate = totalSubmissions > 0 ? (consultationRequests / totalSubmissions) * 100 : 0;

      return {
        summary: {
          totalSubmissions,
          averageCost: Math.round(averageCost),
          consultationRequests,
          conversionRate: Math.round(conversionRate * 100) / 100,
        },
        timeline: [],
        costDistribution: [],
        industryBreakdown: [],
      };
    } catch (error) {
      strapi.log.error('Analytics calculation error:', error);
      throw error;
    }
  },
}));
