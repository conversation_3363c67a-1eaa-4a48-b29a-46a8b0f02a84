# Cloud Cost Calculator - Strapi Schema Documentation

## Overview

This document outlines the comprehensive Strapi schema structure for the Cloud Cost Calculator, based on the AI readiness assessment requirements. The calculator is designed to estimate cloud migration costs across 5 main sections with dynamic pricing logic.

## Schema Architecture

### 1. Main Content Types

#### Cloud Cost Calculator (`cloud-cost-calculator`)
- **Type**: Single Type
- **Purpose**: Main configuration for the calculator page
- **Key Features**:
  - Hero section with title, description, and CTA
  - Relations to calculator sections
  - Global pricing rules
  - Result configuration
  - Form settings and UI customization

#### Calculator Section (`calculator-section`)
- **Type**: Collection Type
- **Purpose**: Organizes questions into logical groups
- **Sections Based on Google Sheets**:
  1. Business & Infrastructure Assessment
  2. Workload & Resource Analysis
  3. Cloud Provider & Deployment Preferences
  4. Security, Compliance & Migration Strategy
  5. Post-Migration & Optimization

#### Calculator Question (`calculator-question`)
- **Type**: Collection Type
- **Purpose**: Individual questions with pricing logic
- **Question Types**:
  - `single_choice`: Radio buttons
  - `multiple_choice`: Checkboxes
  - `range_based`: Server count ranges with tiered pricing
  - `percentage_based`: Adds percentage to base costs
  - `text_input`: Free text
  - `number_input`: Numeric input

#### Calculator Submission (`calculator-submission`)
- **Type**: Collection Type
- **Purpose**: Stores user submissions for analytics and follow-up
- **Features**:
  - User contact information
  - Complete answer set
  - Calculated results
  - Marketing attribution data
  - Follow-up status tracking

### 2. Components

#### Calculator Components (`src/components/calculator/`)

##### Question Option (`question-option.json`)
- Options for questions with pricing values
- Supports conditional pricing based on other answers
- Range-based pricing (min/max values)
- Percentage-based pricing

##### Pricing Rule (`pricing-rule.json`)
- Complex pricing calculations
- Conditional logic based on multiple factors
- Types: fixed, percentage, multiplier, range_based, conditional

##### Rule Condition (`rule-condition.json`)
- Conditions for applying pricing rules
- Operators: equals, contains, greater_than, etc.
- Logic operators: AND, OR

##### Result Configuration (`result-config.json`)
- Customizes result display
- Currency formatting
- Cost breakdown settings
- CTA buttons

##### Cost Labels (`cost-labels.json`)
- Customizable labels for cost categories
- Supports internationalization

##### Recommendation (`recommendation.json`)
- Dynamic recommendations based on answers
- Priority levels and conditions
- Action buttons for follow-up

### 3. Pricing Logic Implementation

#### Based on Google Sheets Data:

1. **Server Count Ranges** (Question 2):
   - < 10 servers: $10,000
   - 10-50: $50,000
   - 50-100: $250,000
   - > 100: $500,000

2. **Data Capacity Ranges** (Question 5):
   - 10GB-50GB: $100
   - 50GB-200GB: $500
   - 200GB-1TB: $2,000
   - 1TB-10TB: $10,000
   - 10TB-50TB: $50,000
   - > 50TB: $150,000

3. **High Availability** (Question 10):
   - Yes: +20% of (Infrastructure + Data Migration)
   - No: +0%

4. **Environment Costs** (Question 13):
   - Dev: $10,000
   - Test: $15,000
   - Staging: $20,000
   - Production: $100,000

5. **Compliance Costs** (Question 14):
   - HIPAA: $20,000
   - GDPR: $10,000
   - PCI DSS: $15,000
   - SOC 2: $15,000
   - CCPA: $5,000
   - FedRAMP: $50,000

6. **Migration Strategy** (Question 15):
   - Lift-and-shift: $5,000
   - Re-platforming: $50,000
   - Re-architecting: $150,000
   - Hybrid: $100,000

7. **Auto-scaling** (Question 16):
   - Yes: $10,000
   - No: $0

## API Endpoints

### Calculator Endpoints
- `GET /api/cloud-cost-calculator` - Get calculator configuration
- `POST /api/cloud-cost-calculator/calculate` - Calculate cost based on answers

### Submission Endpoints
- `POST /api/calculator-submissions` - Submit calculator results
- `GET /api/calculator-submissions/analytics` - Get analytics data

### Section & Question Management
- `GET /api/calculator-sections` - Get all sections
- `GET /api/calculator-questions` - Get all questions

## Usage Examples

### 1. Setting Up Calculator Sections

```json
{
  "title": "Business & Infrastructure Assessment",
  "description": "Assess your current infrastructure and migration scope",
  "section_number": 1,
  "section_weight": 1.0,
  "section_identifier": "business_infrastructure",
  "is_active": true
}
```

### 2. Creating Questions with Pricing

```json
{
  "question_text": "Approximately how many servers do you intend to migrate?",
  "question_type": "range_based",
  "question_identifier": "server_count",
  "question_number": 2,
  "options": [
    {
      "label": "< 10 servers",
      "value": "under_10",
      "min_value": 0,
      "max_value": 9,
      "cost": 10000
    },
    {
      "label": "10 – 50",
      "value": "10_to_50",
      "min_value": 10,
      "max_value": 50,
      "cost": 50000
    }
  ]
}
```

### 3. Percentage-based Pricing Rule

```json
{
  "name": "High Availability Premium",
  "calculation_type": "percentage",
  "value": 20,
  "applies_to": "base_infrastructure",
  "conditions": [
    {
      "question_identifier": "high_availability",
      "operator": "equals",
      "value": "yes"
    }
  ]
}
```

## Best Practices

1. **Question Identifiers**: Use consistent, descriptive identifiers
2. **Pricing Rules**: Order by priority for complex calculations
3. **Validation**: Implement proper validation rules for user inputs
4. **Analytics**: Track user behavior and conversion rates
5. **Performance**: Use proper indexing for large datasets
6. **Security**: Validate all inputs and sanitize data

## Integration Notes

- Compatible with existing Strapi components (common.button, seo.seo, etc.)
- Supports email notifications for follow-ups
- Analytics integration ready
- CRM integration hooks available
- Supports UTM tracking for marketing attribution

## Final Cost Calculation

The final cost is calculated as:
1. Sum all section costs based on user answers
2. Apply global pricing rules (like high availability percentage)
3. Add fixed costs (compliance, migration strategy, etc.)
4. Calculate upper range (30% higher than base estimate)

**Example**: Base cost $525,000, Upper range $682,500 (30% increase)
