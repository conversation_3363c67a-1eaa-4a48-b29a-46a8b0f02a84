/**
 * calculator-submission router
 */

import { factories } from '@strapi/strapi';

const defaultRouter = factories.createCoreRouter('api::calculator-submission.calculator-submission' as any);

export default {
  routes: [
    {
      method: 'GET',
      path: '/calculator-submissions',
      handler: 'calculator-submission.find',
    },
    {
      method: 'POST',
      path: '/calculator-submissions',
      handler: 'calculator-submission.create',
    },
    {
      method: 'GET',
      path: '/calculator-submissions/analytics',
      handler: 'calculator-submission.analytics',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
