/**
 * calculator-submission router
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreRouter('api::calculator-submission.calculator-submission', {
  config: {
    create: {
      middlewares: [],
    },
    analytics: {
      method: 'GET',
      path: '/calculator-submissions/analytics',
      handler: 'calculator-submission.analytics',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  },
});
