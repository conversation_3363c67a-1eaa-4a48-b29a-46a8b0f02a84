/**
 * Migration runner for seeding the Cloud Cost Calculator
 * Run with: node database/migrations/run-seed.js
 */

const { seedCloudCalculator } = require('./seed-cloud-calculator');

// Mock Strapi object for testing
const mockStrapi = {
  entityService: {
    create: async (contentType, options) => {
      console.log(`<PERSON><PERSON> creating ${contentType}:`, JSON.stringify(options.data, null, 2));
      return { 
        id: Math.floor(Math.random() * 1000),
        ...options.data 
      };
    },
  },
};

async function runSeed() {
  try {
    console.log('🚀 Starting Cloud Cost Calculator seed...');
    await seedCloudCalculator(mockStrapi);
    console.log('✅ Seed completed successfully!');
  } catch (error) {
    console.error('❌ Seed failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  runSeed();
}

module.exports = { runSeed };
