# Windows Upload Troubleshooting Guide

## 🚨 Current Issue: EBUSY Error

You're encountering this error:
```
EBUSY: resource busy or locked, unlink 'C:\Users\<USER>\AppData\Local\Temp\strapi-upload-qKZ3JN\optimized-large_blonde_young_businesswoman_working_seriously_laptop_modern_office_8faaed3925_6c7d7c1f9c'
```

This is a common Windows-specific issue where Strapi cannot delete temporary files because they're still being used by another process.

## 🔧 Solutions Applied

### 1. Configuration Updates
I've added timeout configurations to help with file handling:

```typescript
actionOptions: {
  upload: {
    timeout: 30000, // 30 seconds timeout
  },
  uploadStream: {
    timeout: 30000,
  },
},
```

### 2. Security Fix
✅ **Fixed**: Moved AWS credentials back to environment variables (never hardcode credentials!)

## 🛠️ Additional Solutions to Try

### Solution 1: Restart Strapi Application
```bash
# Stop the current Strapi process (Ctrl+C)
# Then restart:
npm run develop
```

### Solution 2: Clear Temporary Files
1. **Close Strapi completely**
2. **Navigate to temp directory**: `C:\Users\<USER>\AppData\Local\Temp\`
3. **Delete Strapi upload folders**: Look for folders starting with `strapi-upload-`
4. **Restart Strapi**

### Solution 3: Check for File Locks
1. **Task Manager**: Check if any processes are holding files
2. **Antivirus**: Temporarily disable real-time scanning for the temp directory
3. **File Explorer**: Close any file explorer windows showing temp directories

### Solution 4: Windows-Specific Configuration
Add this to your `config/server.ts` (create if it doesn't exist):

```typescript
export default ({ env }) => ({
  host: env('HOST', '0.0.0.0'),
  port: env.int('PORT', 1337),
  app: {
    keys: env.array('APP_KEYS'),
  },
  // Windows-specific settings
  settings: {
    cors: {
      enabled: true,
    },
    // Increase timeout for file operations
    timeout: 30000,
  },
});
```

### Solution 5: Alternative Upload Method
If the issue persists, try uploading smaller files first to test if it's size-related.

## 🔍 Debugging Steps

### Step 1: Check File Permissions
```bash
# Run PowerShell as Administrator
# Check if temp directory has proper permissions
icacls "C:\Users\<USER>\AppData\Local\Temp\"
```

### Step 2: Monitor File Handles
Use Process Monitor (ProcMon) to see which process is holding the file:
1. Download Process Monitor from Microsoft
2. Filter by process name: `node.exe`
3. Try uploading a file
4. Look for file access patterns

### Step 3: Check Disk Space
Ensure you have sufficient disk space in:
- `C:\Users\<USER>\AppData\Local\Temp\`
- Your project directory
- System drive

## 🚀 Prevention Measures

### 1. Regular Cleanup
Add a cleanup script to your `package.json`:
```json
{
  "scripts": {
    "clean-temp": "rimraf \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\strapi-upload-*\"",
    "dev-clean": "npm run clean-temp && npm run develop"
  }
}
```

### 2. Antivirus Exclusions
Add these directories to your antivirus exclusions:
- Your project directory
- `C:\Users\<USER>\AppData\Local\Temp\`
- Node.js installation directory

### 3. Windows Defender Exclusions
1. Open Windows Security
2. Go to Virus & threat protection
3. Add exclusions for the directories above

## 🔄 If Issue Persists

### Option 1: Use Different Upload Provider
Consider switching to a different upload method temporarily:
- Local file system upload
- Different cloud provider

### Option 2: Docker Alternative
Run Strapi in Docker to avoid Windows file system issues:
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 1337
CMD ["npm", "run", "develop"]
```

### Option 3: WSL2 (Windows Subsystem for Linux)
Run your development environment in WSL2 to avoid Windows-specific file handling issues.

## 📞 Next Steps

1. **Immediate**: Restart Strapi and try uploading again
2. **Short-term**: Clear temp files and check antivirus settings
3. **Long-term**: Consider WSL2 or Docker for development

## ⚠️ Security Reminder

✅ **Fixed**: AWS credentials are now properly stored in `.env` file
🔒 **Important**: Never commit `.env` file to version control
🔑 **Best Practice**: Rotate AWS keys regularly

The configuration is now secure and should handle file uploads better on Windows!
