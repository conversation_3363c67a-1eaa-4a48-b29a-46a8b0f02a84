/**
 * calculator-submission controller
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::calculator-submission.calculator-submission', ({ strapi }) => ({
  async create(ctx) {
    try {
      const { data } = ctx.request.body;
      
      // Add metadata
      const submissionData = {
        ...data,
        ip_address: ctx.request.ip,
        user_agent: ctx.request.header['user-agent'],
        referrer: ctx.request.header.referer,
        utm_source: ctx.query.utm_source,
        utm_medium: ctx.query.utm_medium,
        utm_campaign: ctx.query.utm_campaign,
      };

      const result = await super.create(ctx);
      
      // Trigger follow-up actions if needed
      if (submissionData.follow_up_requested || submissionData.consultation_requested) {
        await strapi.service('api::calculator-submission.calculator-submission').handleFollowUp(result.data);
      }

      return result;
    } catch (error) {
      strapi.log.error('Calculator submission error:', error);
      return ctx.internalServerError('Error saving submission');
    }
  },

  async analytics(ctx) {
    try {
      const { startDate, endDate, groupBy = 'day' } = ctx.query;
      
      const analytics = await strapi.service('api::calculator-submission.calculator-submission').getAnalytics({
        startDate,
        endDate,
        groupBy
      });

      return { data: analytics };
    } catch (error) {
      strapi.log.error('Analytics error:', error);
      return ctx.internalServerError('Error fetching analytics');
    }
  }
}));
