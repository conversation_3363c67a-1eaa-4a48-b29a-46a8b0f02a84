# AWS S3 + CloudFront Upload Configuration Guide

This guide explains how to configure AWS S3 with CloudFront for secure file uploads in your Strapi application, replacing the previous Google Cloud Platform (GCP) setup. This configuration uses a **private S3 bucket** with **CloudFront distribution** for public file access.

## Architecture Overview

- **S3 Bucket**: Private bucket with "Block all public access" enabled
- **CloudFront**: CDN distribution for public file access
- **Security**: Files served through CloudFront, not direct S3 URLs
- **Performance**: Global CDN for faster file delivery

## Changes Made

### 1. Dependencies Updated

- **Installed**: `@strapi/provider-upload-aws-s3` - Official Strapi AWS S3 provider
- **Removed**: `@strapi-community/strapi-provider-upload-google-cloud-storage` - Previous GCP provider

### 2. Environment Variables Added

The following environment variables have been added to your `.env` file:

```env
# AWS S3 Configuration (Private Bucket + CloudFront)
AWS_ACCESS_KEY_ID=your_aws_access_key_id_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key_here
AWS_REGION=ap-south-1
AWS_S3_BUCKET=maruti-site-cdn

# CloudFront Configuration
CLOUDFRONT_URL=https://your-cloudfront-domain.cloudfront.net
S3_ROOT_PATH=
```

### 3. Plugin Configuration Updated

The `config/plugins.ts` file has been updated with the new AWS S3 configuration:

- **GCP Configuration**: Commented out (preserved for reference)
- **AWS S3 Configuration**: Private bucket configuration without public ACL
- **CloudFront Integration**: Base URL configured for CloudFront distribution

## Setup Instructions

### Step 1: AWS S3 Private Bucket Setup

1. **Create an S3 Bucket**:

   - Log into AWS Console
   - Navigate to S3 service
   - Create a new bucket: `maruti-site-cdn`
   - Choose region: `ap-south-1` (Asia Pacific - Mumbai)

2. **Configure Bucket Security**:

   - **Enable "Block all public access"** ✅
   - This prevents direct public access to S3 objects
   - Files will only be accessible through CloudFront

3. **Set up CORS Configuration** (if needed for uploads):
   ```json
   [
     {
       "AllowedHeaders": ["*"],
       "AllowedMethods": ["GET", "POST", "PUT", "DELETE"],
       "AllowedOrigins": ["*"],
       "ExposeHeaders": []
     }
   ]
   ```

### Step 2: AWS IAM User Setup

1. **Create IAM User**:

   - Go to AWS IAM console
   - Create a new user for Strapi
   - Enable programmatic access

2. **Attach Policies**:
   Create a custom policy with the following permissions (no PutObjectAcl needed for private bucket):

   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Action": ["s3:GetObject", "s3:PutObject", "s3:DeleteObject"],
         "Resource": "arn:aws:s3:::maruti-site-cdn/*"
       },
       {
         "Effect": "Allow",
         "Action": ["s3:ListBucket"],
         "Resource": "arn:aws:s3:::maruti-site-cdn"
       }
     ]
   }
   ```

3. **Get Access Keys**:
   - Generate access key ID and secret access key
   - Store them securely

### Step 3: CloudFront Distribution Setup

1. **Create CloudFront Distribution**:

   - Go to AWS CloudFront console
   - Create a new distribution
   - **Origin Domain**: Select your S3 bucket `maruti-site-cdn.s3.ap-south-1.amazonaws.com`
   - **Origin Access**: Choose "Origin Access Control (OAC)" (recommended) or "Origin Access Identity (OAI)"

2. **Configure Origin Access Control (OAC)**:

   - Create a new OAC if needed
   - This allows CloudFront to access your private S3 bucket
   - Copy the bucket policy provided by CloudFront and apply it to your S3 bucket

3. **Distribution Settings**:

   - **Viewer Protocol Policy**: Redirect HTTP to HTTPS
   - **Allowed HTTP Methods**: GET, HEAD, OPTIONS, PUT, POST, PATCH, DELETE
   - **Cache Behaviors**: Configure as needed for your use case
   - **Price Class**: Choose based on your global reach requirements

4. **Update S3 Bucket Policy**:
   CloudFront will provide a bucket policy. Apply it to your S3 bucket to allow CloudFront access.

5. **Get CloudFront Domain**:
   - After deployment, note your CloudFront domain (e.g., `d1234567890.cloudfront.net`)
   - This will be used in your environment variables

### Step 4: Update Environment Variables

Replace the placeholder values in your `.env` file:

```env
AWS_ACCESS_KEY_ID=AKIA...  # Your actual access key ID
AWS_SECRET_ACCESS_KEY=...  # Your actual secret access key
AWS_REGION=ap-south-1      # Your bucket's region
AWS_S3_BUCKET=maruti-site-cdn  # Your bucket name

# CloudFront Configuration
CLOUDFRONT_URL=https://d1234567890.cloudfront.net  # Your CloudFront domain
S3_ROOT_PATH=  # Optional: subdirectory in bucket
```

### Step 5: Test the Configuration

1. Start your Strapi application: `npm run develop`
2. Go to the admin panel
3. Try uploading a file through the Media Library
4. Verify the file appears in your S3 bucket
5. **Important**: Check that file URLs use your CloudFront domain, not direct S3 URLs
6. Test file access through the CloudFront URL

## Configuration Options

The current private bucket + CloudFront configuration includes:

- **Private S3 Bucket**: No public ACL - files stored securely
- **CloudFront Distribution**: Public access through CDN
- **Signed URL Expiration**: 15 minutes for admin operations
- **Region**: `ap-south-1` (Asia Pacific - Mumbai)
- **Bucket**: `maruti-site-cdn`
- **Base URL**: CloudFront distribution domain

## Security Best Practices

1. **Environment Variables**: Never commit AWS credentials to version control
2. **IAM Permissions**: Use minimal required permissions (no PutObjectAcl needed)
3. **Private S3 Bucket**: Block all public access - files only accessible via CloudFront
4. **Origin Access Control**: Use OAC (recommended) or OAI for CloudFront-S3 access
5. **Access Keys**: Rotate regularly
6. **Monitoring**: Enable CloudTrail for API logging
7. **HTTPS Only**: Configure CloudFront to redirect HTTP to HTTPS

## Troubleshooting

### Common Issues:

1. **Access Denied**: Check IAM permissions and S3 bucket policy for CloudFront OAC
2. **Region Mismatch**: Ensure AWS_REGION matches your bucket's region (`ap-south-1`)
3. **CORS Errors**: Configure CORS policy on your S3 bucket
4. **File Not Found**: Verify bucket name and CloudFront distribution
5. **Direct S3 URLs Not Working**: This is expected - files should be accessed via CloudFront
6. **CloudFront Cache Issues**: Files may take time to propagate or need cache invalidation

### Debug Steps:

1. Check Strapi logs for detailed error messages
2. Verify AWS credentials using AWS CLI
3. Test bucket access independently
4. Check network connectivity

## Migration Notes

- **File URLs**: Existing file URLs from GCP will need to be updated
- **File Migration**: Consider migrating existing files from GCP to S3
- **CDN**: You may want to set up CloudFront for better performance
- **Backup**: Keep GCP configuration commented for rollback if needed

## Additional Features

You can extend the configuration with:

- **Custom file naming**: Modify the upload configuration
- **File size limits**: Adjust in provider options
- **Multiple buckets**: Different buckets for different file types
- **Encryption**: Enable server-side encryption
- **Lifecycle policies**: Automatic file archiving/deletion

## Support

For issues related to:

- **Strapi AWS S3 Provider**: Check official Strapi documentation
- **AWS S3**: Refer to AWS S3 documentation
- **Configuration**: Review this guide and Strapi plugin documentation
