/**
 * Seed script for Cloud Cost Calculator
 * Based on the Google Sheets AI Readiness data
 */

const seedCloudCalculator = async (strapi) => {
  try {
    console.log("🌱 Seeding Cloud Cost Calculator...");

    // 1. Create Calculator Sections
    const sections = [
      {
        title: "Business & Infrastructure Assessment",
        description: "Assess your current infrastructure and migration scope",
        section_number: 1,
        section_identifier: "business_infrastructure",
        section_weight: 1.0,
        is_active: true,
        publishedAt: new Date(),
      },
      {
        title: "Workload & Resource Analysis",
        description: "Analyze your workloads and resource requirements",
        section_number: 2,
        section_identifier: "workload_analysis",
        section_weight: 1.0,
        is_active: true,
        publishedAt: new Date(),
      },
      {
        title: "Cloud Provider & Deployment Preferences",
        description: "Choose your cloud provider and deployment strategy",
        section_number: 3,
        section_identifier: "cloud_deployment",
        section_weight: 1.0,
        is_active: true,
        publishedAt: new Date(),
      },
      {
        title: "Security, Compliance & Migration Strategy",
        description: "Define security requirements and migration approach",
        section_number: 4,
        section_identifier: "security_compliance",
        section_weight: 1.0,
        is_active: true,
        publishedAt: new Date(),
      },
      {
        title: "Post-Migration & Optimization",
        description: "Plan for post-migration optimization and management",
        section_number: 5,
        section_identifier: "post_migration",
        section_weight: 1.0,
        is_active: true,
        publishedAt: new Date(),
      },
    ];

    const createdSections = [];
    for (const sectionData of sections) {
      const section = await strapi.entityService.create(
        "api::calculator-section.calculator-section",
        {
          data: sectionData,
        }
      );
      createdSections.push(section);
      console.log(`✅ Created section: ${section.title}`);
    }

    // 2. Create Questions with Options
    const questions = [
      // Section 1: Business & Infrastructure Assessment
      {
        question_text:
          "Which elements are you planning to migrate to the cloud?",
        question_type: "single_choice",
        question_identifier: "migration_elements",
        question_number: 1,
        section: createdSections[0].id,
        is_required: true,
        options: [
          {
            label: "All infrastructure components (servers, network, OS)",
            value: "all_infrastructure",
            cost: 0,
          },
          { label: "Applications only", value: "applications_only", cost: 0 },
          { label: "Data only", value: "data_only", cost: 0 },
          { label: "Hybrid approach", value: "hybrid", cost: 0 },
        ],
      },
      {
        question_text:
          "Approximately how many servers do you intend to migrate?",
        question_type: "range_based",
        question_identifier: "server_count",
        question_number: 2,
        section: createdSections[0].id,
        is_required: true,
        options: [
          {
            label: "< 10 servers",
            value: "under_10",
            min_value: 0,
            max_value: 9,
            cost: 10000,
          },
          {
            label: "10 – 50",
            value: "10_to_50",
            min_value: 10,
            max_value: 50,
            cost: 50000,
          },
          {
            label: "50 – 100",
            value: "50_to_100",
            min_value: 50,
            max_value: 100,
            cost: 250000,
          },
          {
            label: "> 100",
            value: "over_100",
            min_value: 101,
            max_value: 999999,
            cost: 500000,
          },
        ],
      },
      {
        question_text: "What is the type of data migration you intend to do?",
        question_type: "single_choice",
        question_identifier: "data_migration_type",
        question_number: 3,
        section: createdSections[0].id,
        is_required: true,
        options: [
          { label: "Homogeneous", value: "homogeneous", cost: 0 },
          { label: "Heterogeneous", value: "heterogeneous", cost: 0 },
        ],
      },
      {
        question_text: "What is your current IT infrastructure setup?",
        question_type: "single_choice",
        question_identifier: "current_infrastructure",
        question_number: 4,
        section: createdSections[0].id,
        is_required: true,
        options: [
          { label: "On-premises", value: "on_premises", cost: 0 },
          { label: "Hybrid", value: "hybrid", cost: 0 },
          { label: "Multi-cloud", value: "multi_cloud", cost: 0 },
        ],
      },
      {
        question_text: "What is the total capacity of your servers?",
        question_type: "range_based",
        question_identifier: "server_capacity",
        question_number: 5,
        section: createdSections[0].id,
        is_required: true,
        options: [
          {
            label: "10 GB – 50 GB",
            value: "10_50_gb",
            min_value: 10,
            max_value: 50,
            cost: 100,
          },
          {
            label: "50 GB – 200 GB",
            value: "50_200_gb",
            min_value: 50,
            max_value: 200,
            cost: 500,
          },
          {
            label: "200 GB – 1 TB",
            value: "200gb_1tb",
            min_value: 200,
            max_value: 1000,
            cost: 2000,
          },
          {
            label: "1 TB – 10 TB",
            value: "1_10_tb",
            min_value: 1000,
            max_value: 10000,
            cost: 10000,
          },
          {
            label: "10 TB – 50 TB",
            value: "10_50_tb",
            min_value: 10000,
            max_value: 50000,
            cost: 50000,
          },
          {
            label: "> 50 TB",
            value: "over_50_tb",
            min_value: 50000,
            max_value: 999999999,
            cost: 150000,
          },
        ],
      },
      {
        question_text:
          "What is the current monthly infrastructure cost of your current setup?",
        question_type: "single_choice",
        question_identifier: "current_monthly_cost",
        question_number: 6,
        section: createdSections[0].id,
        is_required: true,
        options: [
          { label: "Under $1K", value: "under_1k", cost: 0 },
          { label: "$1K – $5K", value: "1k_5k", cost: 0 },
          { label: "$5K – $20K", value: "5k_20k", cost: 0 },
          { label: "Over $20K", value: "over_20k", cost: 0 },
        ],
      },
      {
        question_text:
          "What is the main purpose behind your decision to migrate to the cloud?",
        question_type: "multiple_choice",
        question_identifier: "migration_purpose",
        question_number: 7,
        section: createdSections[0].id,
        is_required: true,
        options: [
          { label: "Lower operational costs", value: "lower_costs", cost: 0 },
          { label: "Improved scalability", value: "scalability", cost: 0 },
          { label: "Better security", value: "security", cost: 0 },
          { label: "Disaster recovery", value: "disaster_recovery", cost: 0 },
          {
            label: "Digital transformation",
            value: "digital_transformation",
            cost: 0,
          },
        ],
      },

      // Section 2: Workload & Resource Analysis
      {
        question_text: "What type of workloads do you run?",
        question_type: "multiple_choice",
        question_identifier: "workload_types",
        question_number: 8,
        section: createdSections[1].id,
        is_required: true,
        options: [
          { label: "Databases", value: "databases", cost: 0 },
          { label: "Web applications", value: "web_apps", cost: 0 },
          { label: "Analytics", value: "analytics", cost: 0 },
          { label: "Machine learning", value: "ml", cost: 0 },
          { label: "File storage", value: "file_storage", cost: 0 },
        ],
      },
      {
        question_text:
          "What is the average CPU and memory usage of your workloads?",
        question_type: "single_choice",
        question_identifier: "resource_usage",
        question_number: 9,
        section: createdSections[1].id,
        is_required: true,
        options: [
          { label: "Low", value: "low", cost: 0 },
          { label: "Medium", value: "medium", cost: 0 },
          { label: "High", value: "high", cost: 0 },
        ],
      },
      {
        question_text:
          "Do you require high availability or disaster recovery for critical applications?",
        question_type: "single_choice",
        question_identifier: "high_availability",
        question_number: 10,
        section: createdSections[1].id,
        is_required: true,
        options: [
          { label: "Yes", value: "yes", cost: 80000, percentage: 20 },
          { label: "No", value: "no", cost: 0 },
        ],
      },

      // Section 3: Cloud Provider & Deployment Preferences
      {
        question_text: "Which cloud provider(s) are you considering?",
        question_type: "single_choice",
        question_identifier: "cloud_provider",
        question_number: 11,
        section: createdSections[2].id,
        is_required: true,
        options: [
          { label: "AWS", value: "aws", cost: 0 },
          { label: "Microsoft Azure", value: "azure", cost: 0 },
          { label: "Google Cloud Platform", value: "gcp", cost: 0 },
          { label: "Multi-cloud", value: "multi_cloud", cost: 0 },
        ],
      },
      {
        question_text:
          "Do you plan to use reserved instances, spot instances, or pay-as-you-go pricing models?",
        question_type: "multiple_choice",
        question_identifier: "pricing_model",
        question_number: 12,
        section: createdSections[2].id,
        is_required: true,
        options: [
          { label: "Reserved instances", value: "reserved", cost: 0 },
          { label: "Spot instances", value: "spot", cost: 0 },
          { label: "Pay-as-you-go", value: "pay_as_go", cost: 0 },
        ],
      },
      {
        question_text: "Which cloud environments are you planning to deploy?",
        question_type: "multiple_choice",
        question_identifier: "environments",
        question_number: 13,
        section: createdSections[2].id,
        is_required: true,
        options: [
          { label: "Development", value: "dev", cost: 10000 },
          { label: "Testing", value: "test", cost: 15000 },
          { label: "Staging", value: "staging", cost: 20000 },
          { label: "Production", value: "production", cost: 100000 },
        ],
      },

      // Section 4: Security, Compliance & Migration Strategy
      {
        question_text:
          "Do you have any specific compliance or regulatory requirements?",
        question_type: "multiple_choice",
        question_identifier: "compliance_requirements",
        question_number: 14,
        section: createdSections[3].id,
        is_required: true,
        options: [
          { label: "HIPAA", value: "hipaa", cost: 20000 },
          { label: "GDPR", value: "gdpr", cost: 10000 },
          { label: "PCI DSS", value: "pci_dss", cost: 15000 },
          { label: "SOC 2", value: "soc2", cost: 15000 },
          { label: "CCPA", value: "ccpa", cost: 5000 },
          { label: "FedRAMP", value: "fedramp", cost: 50000 },
          { label: "None", value: "none", cost: 0 },
        ],
      },
      {
        question_text: "What migration strategy do you prefer?",
        question_type: "single_choice",
        question_identifier: "migration_strategy",
        question_number: 15,
        section: createdSections[3].id,
        is_required: true,
        options: [
          { label: "Lift-and-shift", value: "lift_shift", cost: 5000 },
          { label: "Re-platforming", value: "re_platforming", cost: 50000 },
          { label: "Re-architecting", value: "re_architecting", cost: 150000 },
          { label: "Hybrid", value: "hybrid", cost: 100000 },
        ],
      },

      // Section 5: Post-Migration & Optimization
      {
        question_text:
          "Do you need auto-scaling capabilities for cost optimization?",
        question_type: "single_choice",
        question_identifier: "auto_scaling",
        question_number: 16,
        section: createdSections[4].id,
        is_required: true,
        options: [
          { label: "Yes", value: "yes", cost: 10000 },
          { label: "No", value: "no", cost: 0 },
        ],
      },
      {
        question_text:
          "How often do you plan to review and optimize your cloud expenses?",
        question_type: "single_choice",
        question_identifier: "optimization_frequency",
        question_number: 17,
        section: createdSections[4].id,
        is_required: true,
        options: [
          { label: "Monthly", value: "monthly", cost: 0 },
          { label: "Quarterly", value: "quarterly", cost: 0 },
          { label: "Annually", value: "annually", cost: 0 },
          { label: "As needed", value: "as_needed", cost: 0 },
        ],
      },
    ];

    // Create questions with their options
    for (const questionData of questions) {
      const { options, ...questionWithoutOptions } = questionData;

      const question = await strapi.entityService.create(
        "api::calculator-question.calculator-question",
        {
          data: {
            ...questionWithoutOptions,
            publishedAt: new Date(),
          },
        }
      );

      console.log(
        `✅ Created question: ${question.question_text.substring(0, 50)}...`
      );
    }

    // 3. Create Main Calculator Configuration
    const calculatorConfig = await strapi.entityService.create(
      "api::cloud-cost-calculator.cloud-cost-calculator",
      {
        data: {
          hero_section: {
            title: "Cloud Migration Cost Calculator",
            description:
              "Get an accurate estimate for your cloud migration project based on your specific requirements and infrastructure.",
            button: {
              text: "Start Calculator",
              url: "#calculator",
              variant: "primary",
            },
          },
          result_configuration: {
            result_title: "Your Cloud Migration Cost Estimate",
            result_description:
              "Based on your responses, here's your estimated cloud migration cost range.",
            show_cost_breakdown: true,
            show_recommendations: true,
            show_upper_range: true,
            upper_range_percentage: 30,
            currency_symbol: "$",
            currency_code: "USD",
            number_format: "comma_separated",
            success_message:
              "Thank you for using our calculator! Our team will reach out to discuss your migration strategy.",
          },
          form_settings: {
            show_progress_bar: true,
            show_section_numbers: true,
            allow_back_navigation: true,
            auto_save_progress: true,
            show_question_numbers: true,
            validation_on_blur: true,
            submit_button_text: "Calculate Migration Cost",
            next_button_text: "Next Section",
            previous_button_text: "Previous",
            loading_message: "Calculating your cloud migration cost...",
            error_message:
              "Please complete all required fields before proceeding.",
            analytics_tracking: true,
          },
          cost_breakdown_labels: {
            infrastructure_label: "Infrastructure Migration",
            data_migration_label: "Data Migration",
            compliance_label: "Compliance & Security",
            environment_label: "Environment Setup",
            migration_strategy_label: "Migration Strategy",
            optimization_label: "Post-Migration Optimization",
            high_availability_label: "High Availability & DR",
            total_cost_label: "Total Estimated Cost",
            cost_range_label: "Cost Range",
            lower_range_label: "Lower Estimate",
            upper_range_label: "Upper Estimate (30% buffer)",
          },
          restart_button: {
            text: "Start Over",
            url: "#calculator",
            variant: "secondary",
          },
          consultation_button: {
            text: "Schedule Consultation",
            url: "/contact-us",
            variant: "primary",
          },
          disclaimer:
            "This estimate is based on typical migration scenarios and may vary based on specific requirements, complexity, and timeline. Contact our team for a detailed assessment.",
          publishedAt: new Date(),
        },
      }
    );

    console.log("✅ Created main calculator configuration");
    console.log("🎉 Cloud Cost Calculator seeded successfully!");
  } catch (error) {
    console.error("❌ Error seeding calculator:", error);
    throw error;
  }
};

module.exports = { seedCloudCalculator };
