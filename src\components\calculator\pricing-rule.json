{"collectionName": "components_calculator_pricing_rules", "info": {"displayName": "Pricing Rule", "description": "Rules for calculating costs based on conditions"}, "options": {}, "attributes": {"name": {"type": "string", "required": true}, "description": {"type": "text"}, "calculation_type": {"type": "enumeration", "enum": ["fixed", "percentage", "multiplier", "range_based", "conditional"], "required": true}, "value": {"type": "decimal", "required": true}, "conditions": {"type": "component", "repeatable": true, "component": "calculator.rule-condition"}, "applies_to": {"type": "enumeration", "enum": ["total_cost", "section_cost", "base_infrastructure", "data_migration", "specific_question"]}, "priority": {"type": "integer", "default": 0}, "is_active": {"type": "boolean", "default": true}}}