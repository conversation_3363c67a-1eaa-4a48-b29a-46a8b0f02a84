/**
 * cloud-cost-calculator service
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreService('api::cloud-cost-calculator.cloud-cost-calculator' as any, ({ strapi }) => ({
  async calculateCost(answers: Record<string, any>, calculator: any) {
    let totalCost = 0;
    const breakdown: Record<string, number> = {};
    const recommendations: string[] = [];

    try {
      // Process each section and question
      for (const section of calculator.calculator_sections) {
        let sectionCost = 0;

        for (const question of section.questions) {
          const userAnswer = answers[question.identifier] || answers[`question_${question.id}`];

          if (userAnswer !== undefined) {
            const questionCost = await this.calculateQuestionCost(question, userAnswer, answers);
            sectionCost += questionCost.cost;

            if (questionCost.recommendations?.length > 0) {
              recommendations.push(...questionCost.recommendations);
            }
          }
        }

        if (sectionCost > 0) {
          breakdown[section.title] = sectionCost;
          totalCost += sectionCost;
        }
      }

      // Apply global pricing rules
      if (calculator.pricing_rules?.length > 0) {
        for (const rule of calculator.pricing_rules) {
          const ruleResult = await this.applyPricingRule(rule, answers, totalCost);
          totalCost += ruleResult.additionalCost;

          if (ruleResult.additionalCost > 0) {
            breakdown[rule.name] = ruleResult.additionalCost;
          }
        }
      }

      return {
        totalCost: Math.round(totalCost),
        breakdown,
        recommendations: [...new Set(recommendations)] // Remove duplicates
      };
    } catch (error) {
      strapi.log.error('Cost calculation service error:', error);
      throw error;
    }
  },

  async calculateQuestionCost(question: any, userAnswer: any, allAnswers: Record<string, any>) {
    let cost = 0;
    const recommendations: string[] = [];

    try {
      switch (question.question_type) {
        case 'single_choice':
        case 'multiple_choice':
          if (Array.isArray(userAnswer)) {
            // Multiple choice
            for (const answerId of userAnswer) {
              const option = question.options?.find((opt: any) => opt.id === answerId || opt.value === answerId);
              if (option?.cost) {
                cost += option.cost;
              }
              if (option?.recommendation) {
                recommendations.push(option.recommendation);
              }
            }
          } else {
            // Single choice
            const option = question.options?.find((opt: any) => opt.id === userAnswer || opt.value === userAnswer);
            if (option?.cost) {
              cost += option.cost;
            }
            if (option?.recommendation) {
              recommendations.push(option.recommendation);
            }
          }
          break;

        case 'range_based':
          // Handle range-based pricing (like server count ranges)
          const rangeOption = question.options?.find((opt: any) => {
            if (opt.min_value !== undefined && opt.max_value !== undefined) {
              return userAnswer >= opt.min_value && userAnswer <= opt.max_value;
            }
            return false;
          });
          if (rangeOption?.cost) {
            cost += rangeOption.cost;
          }
          break;

        case 'percentage_based':
          // Handle percentage-based pricing (like high availability +20%)
          const baseInfraCost = this.getBaseCost(allAnswers, ['infrastructure', 'data_migration']);
          const option = question.options?.find((opt: any) => opt.id === userAnswer || opt.value === userAnswer);
          if (option?.percentage) {
            cost += Math.round(baseInfraCost * (option.percentage / 100));
          }
          break;

        default:
          // Handle custom pricing logic
          if (question.pricing_rules?.length > 0) {
            for (const rule of question.pricing_rules) {
              const ruleResult = await this.applyPricingRule(rule, allAnswers, cost);
              cost += ruleResult.additionalCost;
            }
          }
      }

      return { cost, recommendations };
    } catch (error) {
      strapi.log.error('Question cost calculation error:', error);
      return { cost: 0, recommendations: [] };
    }
  },

  async applyPricingRule(rule: any, answers: Record<string, any>, currentCost: number) {
    let additionalCost = 0;

    try {
      // Evaluate rule conditions
      const conditionMet = this.evaluateRuleConditions(rule.conditions, answers);

      if (conditionMet) {
        switch (rule.calculation_type) {
          case 'fixed':
            additionalCost = rule.value || 0;
            break;
          case 'percentage':
            additionalCost = Math.round(currentCost * ((rule.value || 0) / 100));
            break;
          case 'multiplier':
            additionalCost = currentCost * (rule.value || 1) - currentCost;
            break;
        }
      }

      return { additionalCost };
    } catch (error) {
      strapi.log.error('Pricing rule application error:', error);
      return { additionalCost: 0 };
    }
  },

  evaluateRuleConditions(conditions: any[], answers: Record<string, any>): boolean {
    if (!conditions?.length) return true;

    return conditions.every(condition => {
      const answerValue = answers[condition.question_identifier];

      switch (condition.operator) {
        case 'equals':
          return answerValue === condition.value;
        case 'contains':
          return Array.isArray(answerValue) ? answerValue.includes(condition.value) : false;
        case 'greater_than':
          return Number(answerValue) > Number(condition.value);
        case 'less_than':
          return Number(answerValue) < Number(condition.value);
        default:
          return false;
      }
    });
  },

  getBaseCost(answers: Record<string, any>, categories: string[]): number {
    // Helper method to get base cost for percentage calculations
    // This would need to be implemented based on your specific logic
    return 0;
  }
}));
