{"collectionName": "components_calculator_rule_conditions", "info": {"displayName": "Rule Condition", "description": "Conditions for applying pricing rules"}, "options": {}, "attributes": {"question_identifier": {"type": "string", "required": true}, "operator": {"type": "enumeration", "enum": ["equals", "not_equals", "contains", "not_contains", "greater_than", "less_than", "greater_than_or_equal", "less_than_or_equal", "in_range", "not_in_range"], "required": true}, "value": {"type": "string", "required": true}, "secondary_value": {"type": "string"}, "logic_operator": {"type": "enumeration", "enum": ["AND", "OR"], "default": "AND"}}}