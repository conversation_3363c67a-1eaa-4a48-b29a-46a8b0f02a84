{"kind": "collectionType", "collectionName": "calculator_submissions", "info": {"singularName": "calculator-submission", "pluralName": "calculator-submissions", "displayName": "Calculator Submission", "description": "Stores calculator submissions for analytics and follow-up"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"submission_id": {"type": "uid", "targetField": "email"}, "user_answers": {"type": "json", "required": true}, "calculated_cost": {"type": "decimal", "required": true}, "cost_breakdown": {"type": "json"}, "recommendations": {"type": "json"}, "email": {"type": "email"}, "company_name": {"type": "string"}, "contact_name": {"type": "string"}, "phone": {"type": "string"}, "industry": {"type": "string"}, "company_size": {"type": "enumeration", "enum": ["startup", "small", "medium", "large", "enterprise"]}, "follow_up_requested": {"type": "boolean", "default": false}, "consultation_requested": {"type": "boolean", "default": false}, "ip_address": {"type": "string"}, "user_agent": {"type": "text"}, "referrer": {"type": "string"}, "utm_source": {"type": "string"}, "utm_medium": {"type": "string"}, "utm_campaign": {"type": "string"}, "session_duration": {"type": "integer"}, "completion_rate": {"type": "decimal"}, "status": {"type": "enumeration", "enum": ["submitted", "contacted", "qualified", "converted", "closed"], "default": "submitted"}, "notes": {"type": "text"}, "assigned_to": {"type": "string"}}}