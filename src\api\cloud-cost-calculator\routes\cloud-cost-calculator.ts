/**
 * cloud-cost-calculator router
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreRouter('api::cloud-cost-calculator.cloud-cost-calculator', {
  config: {
    find: {
      middlewares: [],
    },
    calculateCost: {
      method: 'POST',
      path: '/cloud-cost-calculator/calculate',
      handler: 'cloud-cost-calculator.calculateCost',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  },
});
