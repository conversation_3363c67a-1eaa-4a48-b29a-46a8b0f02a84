/**
 * cloud-cost-calculator router
 */

export default {
  routes: [
    {
      method: 'GET',
      path: '/cloud-cost-calculator',
      handler: 'cloud-cost-calculator.find',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/cloud-cost-calculator/calculate',
      handler: 'cloud-cost-calculator.calculateCost',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
