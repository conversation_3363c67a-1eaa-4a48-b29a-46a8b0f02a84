/**
 * Test script for Cloud Cost Calculator API
 * Run with: node scripts/test-calculator-api.js
 */

const axios = require('axios');

const BASE_URL = process.env.STRAPI_URL || 'http://localhost:1337';

async function testCalculatorAPI() {
  try {
    console.log('🧪 Testing Cloud Cost Calculator API...');

    // Test 1: Get calculator configuration
    console.log('\n1. Testing GET /api/cloud-cost-calculator');
    try {
      const configResponse = await axios.get(`${BASE_URL}/api/cloud-cost-calculator`);
      console.log('✅ Calculator configuration retrieved successfully');
      console.log('   Sections found:', configResponse.data?.data?.calculator_sections?.length || 0);
    } catch (error) {
      console.log('❌ Failed to get calculator configuration:', error.message);
    }

    // Test 2: Calculate cost with sample data
    console.log('\n2. Testing POST /api/cloud-cost-calculator/calculate');
    const sampleAnswers = {
      server_count: '50_to_100',
      server_capacity: '10_50_tb',
      high_availability: 'yes',
      environments: ['dev', 'staging', 'production'],
      compliance_requirements: ['gdpr', 'soc2'],
      migration_strategy: 're_platforming',
      auto_scaling: 'yes'
    };

    try {
      const calcResponse = await axios.post(`${BASE_URL}/api/cloud-cost-calculator/calculate`, {
        answers: sampleAnswers
      });
      console.log('✅ Cost calculation successful');
      console.log('   Total Cost:', calcResponse.data?.data?.totalCost);
      console.log('   Upper Range:', calcResponse.data?.data?.upperRange);
    } catch (error) {
      console.log('❌ Failed to calculate cost:', error.message);
    }

    // Test 3: Submit calculator results
    console.log('\n3. Testing POST /api/calculator-submissions');
    const sampleSubmission = {
      data: {
        user_answers: sampleAnswers,
        calculated_cost: 575000,
        cost_breakdown: {
          'Infrastructure Migration': 250000,
          'Data Migration': 50000,
          'Environment Setup': 130000,
          'Compliance & Security': 25000,
          'Migration Strategy': 50000,
          'Post-Migration Optimization': 10000,
          'High Availability & DR': 60000
        },
        email: '<EMAIL>',
        company_name: 'Test Company',
        contact_name: 'John Doe',
        industry: 'Technology',
        company_size: 'medium',
        follow_up_requested: true,
        consultation_requested: false
      }
    };

    try {
      const submissionResponse = await axios.post(`${BASE_URL}/api/calculator-submissions`, sampleSubmission);
      console.log('✅ Submission successful');
      console.log('   Submission ID:', submissionResponse.data?.data?.id);
    } catch (error) {
      console.log('❌ Failed to submit:', error.message);
    }

    // Test 4: Get analytics (if submissions exist)
    console.log('\n4. Testing GET /api/calculator-submissions/analytics');
    try {
      const analyticsResponse = await axios.get(`${BASE_URL}/api/calculator-submissions/analytics?startDate=2024-01-01&endDate=2024-12-31`);
      console.log('✅ Analytics retrieved successfully');
      console.log('   Total Submissions:', analyticsResponse.data?.data?.summary?.totalSubmissions || 0);
      console.log('   Average Cost:', analyticsResponse.data?.data?.summary?.averageCost || 0);
    } catch (error) {
      console.log('❌ Failed to get analytics:', error.message);
    }

    console.log('\n🎉 API testing completed!');

  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
  }
}

// Sample cost calculation based on Google Sheets data
function calculateExpectedCost(answers) {
  let totalCost = 0;
  
  // Server count (Question 2)
  if (answers.server_count === '50_to_100') {
    totalCost += 250000;
  }
  
  // Server capacity (Question 5)  
  if (answers.server_capacity === '10_50_tb') {
    totalCost += 50000;
  }
  
  // Environments (Question 13)
  if (answers.environments?.includes('dev')) totalCost += 10000;
  if (answers.environments?.includes('staging')) totalCost += 20000;
  if (answers.environments?.includes('production')) totalCost += 100000;
  
  // Compliance (Question 14)
  if (answers.compliance_requirements?.includes('gdpr')) totalCost += 10000;
  if (answers.compliance_requirements?.includes('soc2')) totalCost += 15000;
  
  // Migration strategy (Question 15)
  if (answers.migration_strategy === 're_platforming') {
    totalCost += 50000;
  }
  
  // Auto-scaling (Question 16)
  if (answers.auto_scaling === 'yes') {
    totalCost += 10000;
  }
  
  // High availability (Question 10) - 20% of infrastructure + data
  if (answers.high_availability === 'yes') {
    const baseInfraCost = 250000 + 50000; // server + capacity
    totalCost += Math.round(baseInfraCost * 0.2);
  }
  
  return totalCost;
}

// Run tests if called directly
if (require.main === module) {
  testCalculatorAPI();
}

module.exports = { testCalculatorAPI, calculateExpectedCost };
