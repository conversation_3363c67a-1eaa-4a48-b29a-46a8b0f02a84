{"collectionName": "components_calculator_validation_rules", "info": {"displayName": "Validation Rule", "description": "Validation rules for question inputs"}, "options": {}, "attributes": {"min_value": {"type": "decimal"}, "max_value": {"type": "decimal"}, "min_length": {"type": "integer"}, "max_length": {"type": "integer"}, "pattern": {"type": "string"}, "error_message": {"type": "string"}, "custom_validation": {"type": "text"}}}