/**
 * Migration: Seed Cloud Cost Calculator
 * Based on the Google Sheets AI Readiness data
 */

module.exports = {
  async up(knex, strapi) {
    console.log('🌱 Seeding Cloud Cost Calculator...');

    try {
      // 1. Create Calculator Sections
      const sections = [
        {
          title: 'Business & Infrastructure Assessment',
          description: 'Assess your current infrastructure and migration scope',
          section_number: 1,
          section_identifier: 'business_infrastructure',
          section_weight: 1.0,
          is_active: true,
          publishedAt: new Date(),
        },
        {
          title: 'Workload & Resource Analysis',
          description: 'Analyze your workloads and resource requirements',
          section_number: 2,
          section_identifier: 'workload_analysis',
          section_weight: 1.0,
          is_active: true,
          publishedAt: new Date(),
        },
        {
          title: 'Cloud Provider & Deployment Preferences',
          description: 'Choose your cloud provider and deployment strategy',
          section_number: 3,
          section_identifier: 'cloud_deployment',
          section_weight: 1.0,
          is_active: true,
          publishedAt: new Date(),
        },
        {
          title: 'Security, Compliance & Migration Strategy',
          description: 'Define security requirements and migration approach',
          section_number: 4,
          section_identifier: 'security_compliance',
          section_weight: 1.0,
          is_active: true,
          publishedAt: new Date(),
        },
        {
          title: 'Post-Migration & Optimization',
          description: 'Plan for post-migration optimization and management',
          section_number: 5,
          section_identifier: 'post_migration',
          section_weight: 1.0,
          is_active: true,
          publishedAt: new Date(),
        },
      ];

      const createdSections = [];
      for (const sectionData of sections) {
        const section = await strapi.entityService.create('api::calculator-section.calculator-section', {
          data: sectionData,
        });
        createdSections.push(section);
        console.log(`✅ Created section: ${section.title}`);
      }

      // 2. Create sample questions (simplified for migration)
      const sampleQuestions = [
        {
          question_text: 'Approximately how many servers do you intend to migrate?',
          question_type: 'single_choice',
          question_identifier: 'server_count',
          question_number: 1,
          section: createdSections[0].id,
          is_required: true,
          publishedAt: new Date(),
        },
        {
          question_text: 'What is the total capacity of your servers?',
          question_type: 'single_choice',
          question_identifier: 'server_capacity',
          question_number: 2,
          section: createdSections[0].id,
          is_required: true,
          publishedAt: new Date(),
        },
        {
          question_text: 'Do you require high availability or disaster recovery?',
          question_type: 'single_choice',
          question_identifier: 'high_availability',
          question_number: 3,
          section: createdSections[1].id,
          is_required: true,
          publishedAt: new Date(),
        },
      ];

      for (const questionData of sampleQuestions) {
        const question = await strapi.entityService.create('api::calculator-question.calculator-question', {
          data: questionData,
        });
        console.log(`✅ Created question: ${question.question_text.substring(0, 50)}...`);
      }

      // 3. Create Main Calculator Configuration
      const calculatorConfig = await strapi.entityService.create('api::cloud-cost-calculator.cloud-cost-calculator', {
        data: {
          hero_section: {
            title: 'Cloud Migration Cost Calculator',
            description: 'Get an accurate estimate for your cloud migration project based on your specific requirements and infrastructure.',
          },
          disclaimer: 'This estimate is based on typical migration scenarios and may vary based on specific requirements, complexity, and timeline. Contact our team for a detailed assessment.',
          publishedAt: new Date(),
        },
      });

      console.log('✅ Created main calculator configuration');
      console.log('🎉 Cloud Cost Calculator seeded successfully!');
    } catch (error) {
      console.error('❌ Error seeding calculator:', error);
      throw error;
    }
  },

  async down(knex, strapi) {
    console.log('🧹 Cleaning up calculator data...');
    
    try {
      // Delete in reverse order due to relationships
      const calculator = await strapi.entityService.findMany('api::cloud-cost-calculator.cloud-cost-calculator');
      if (calculator && calculator.id) {
        await strapi.entityService.delete('api::cloud-cost-calculator.cloud-cost-calculator', calculator.id);
      }
      
      const questions = await strapi.entityService.findMany('api::calculator-question.calculator-question');
      if (Array.isArray(questions)) {
        for (const question of questions) {
          await strapi.entityService.delete('api::calculator-question.calculator-question', question.id);
        }
      }
      
      const sections = await strapi.entityService.findMany('api::calculator-section.calculator-section');
      if (Array.isArray(sections)) {
        for (const section of sections) {
          await strapi.entityService.delete('api::calculator-section.calculator-section', section.id);
        }
      }
      
      console.log('✅ Calculator data cleaned up');
    } catch (error) {
      console.error('❌ Error cleaning up:', error);
    }
  }
};
