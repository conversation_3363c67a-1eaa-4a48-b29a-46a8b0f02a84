/**
 * Manual seeding script for Cloud Cost Calculator
 * Run this after <PERSON><PERSON><PERSON> is running: node scripts/seed-calculator.js
 */

const axios = require('axios');

const BASE_URL = process.env.STRAPI_URL || 'http://localhost:1337';
const API_TOKEN = process.env.STRAPI_API_TOKEN; // Optional: for authenticated requests

const axiosConfig = {
  headers: {
    'Content-Type': 'application/json',
    ...(API_TOKEN && { 'Authorization': `Bearer ${API_TOKEN}` })
  }
};

async function seedCalculator() {
  try {
    console.log('🌱 Starting Cloud Cost Calculator seeding...');

    // 1. Create Calculator Sections
    console.log('\n📁 Creating calculator sections...');
    const sections = [
      {
        title: 'Business & Infrastructure Assessment',
        description: 'Assess your current infrastructure and migration scope',
        section_number: 1,
        section_identifier: 'business_infrastructure',
        section_weight: 1.0,
        is_active: true,
      },
      {
        title: 'Workload & Resource Analysis',
        description: 'Analyze your workloads and resource requirements',
        section_number: 2,
        section_identifier: 'workload_analysis',
        section_weight: 1.0,
        is_active: true,
      },
      {
        title: 'Cloud Provider & Deployment Preferences',
        description: 'Choose your cloud provider and deployment strategy',
        section_number: 3,
        section_identifier: 'cloud_deployment',
        section_weight: 1.0,
        is_active: true,
      },
      {
        title: 'Security, Compliance & Migration Strategy',
        description: 'Define security requirements and migration approach',
        section_number: 4,
        section_identifier: 'security_compliance',
        section_weight: 1.0,
        is_active: true,
      },
      {
        title: 'Post-Migration & Optimization',
        description: 'Plan for post-migration optimization and management',
        section_number: 5,
        section_identifier: 'post_migration',
        section_weight: 1.0,
        is_active: true,
      },
    ];

    const createdSections = [];
    for (const sectionData of sections) {
      try {
        const response = await axios.post(`${BASE_URL}/api/calculator-sections`, {
          data: sectionData
        }, axiosConfig);
        createdSections.push(response.data.data);
        console.log(`✅ Created section: ${sectionData.title}`);
      } catch (error) {
        console.log(`❌ Failed to create section: ${sectionData.title}`, error.response?.data || error.message);
      }
    }

    // 2. Create Sample Questions
    console.log('\n❓ Creating sample questions...');
    const sampleQuestions = [
      {
        question_text: 'Approximately how many servers do you intend to migrate?',
        question_type: 'single_choice',
        question_identifier: 'server_count',
        question_number: 1,
        section: createdSections[0]?.id,
        is_required: true,
        help_text: 'Select the range that best matches your server count',
      },
      {
        question_text: 'What is the total capacity of your servers?',
        question_type: 'single_choice',
        question_identifier: 'server_capacity',
        question_number: 2,
        section: createdSections[0]?.id,
        is_required: true,
        help_text: 'Total storage capacity across all servers',
      },
      {
        question_text: 'Do you require high availability or disaster recovery for critical applications?',
        question_type: 'single_choice',
        question_identifier: 'high_availability',
        question_number: 3,
        section: createdSections[1]?.id,
        is_required: true,
        help_text: 'High availability adds approximately 20% to infrastructure costs',
      },
      {
        question_text: 'Which cloud provider(s) are you considering?',
        question_type: 'single_choice',
        question_identifier: 'cloud_provider',
        question_number: 4,
        section: createdSections[2]?.id,
        is_required: true,
      },
      {
        question_text: 'Do you have any specific compliance or regulatory requirements?',
        question_type: 'multiple_choice',
        question_identifier: 'compliance_requirements',
        question_number: 5,
        section: createdSections[3]?.id,
        is_required: true,
        help_text: 'Select all that apply to your organization',
      },
    ];

    for (const questionData of sampleQuestions) {
      if (questionData.section) {
        try {
          const response = await axios.post(`${BASE_URL}/api/calculator-questions`, {
            data: questionData
          }, axiosConfig);
          console.log(`✅ Created question: ${questionData.question_text.substring(0, 50)}...`);
        } catch (error) {
          console.log(`❌ Failed to create question: ${questionData.question_identifier}`, error.response?.data || error.message);
        }
      }
    }

    // 3. Create Main Calculator Configuration
    console.log('\n⚙️ Creating main calculator configuration...');
    try {
      const calculatorConfig = {
        hero_section: {
          title: 'Cloud Migration Cost Calculator',
          description: 'Get an accurate estimate for your cloud migration project based on your specific requirements and infrastructure.',
          button: {
            text: 'Start Calculator',
            url: '#calculator',
            variant: 'primary',
          },
        },
        disclaimer: 'This estimate is based on typical migration scenarios and may vary based on specific requirements, complexity, and timeline. Contact our team for a detailed assessment.',
      };

      const response = await axios.post(`${BASE_URL}/api/cloud-cost-calculator`, {
        data: calculatorConfig
      }, axiosConfig);
      
      console.log('✅ Created main calculator configuration');
    } catch (error) {
      console.log('❌ Failed to create calculator configuration:', error.response?.data || error.message);
    }

    console.log('\n🎉 Cloud Cost Calculator seeding completed!');
    console.log('\n📋 Next steps:');
    console.log('1. Visit Strapi admin to add question options and pricing');
    console.log('2. Configure pricing rules for each question');
    console.log('3. Test the calculator API endpoints');

  } catch (error) {
    console.error('❌ Seeding failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  seedCalculator();
}

module.exports = { seedCalculator };
