{"kind": "collectionType", "collectionName": "calculator_questions", "info": {"singularName": "calculator-question", "pluralName": "calculator-questions", "displayName": "Calculator Question", "description": "Individual questions for the cloud cost calculator with pricing logic"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"question_text": {"type": "text", "required": true}, "question_number": {"type": "integer", "required": true}, "question_type": {"type": "enumeration", "enum": ["single_choice", "multiple_choice", "range_based", "percentage_based", "text_input", "number_input"], "required": true}, "question_identifier": {"type": "string", "required": true, "unique": true}, "section": {"type": "relation", "relation": "manyToOne", "target": "api::calculator-section.calculator-section", "inversedBy": "questions"}, "options": {"type": "component", "repeatable": true, "component": "calculator.question-option"}, "is_required": {"type": "boolean", "default": true}, "help_text": {"type": "text"}, "validation_rules": {"type": "component", "repeatable": false, "component": "calculator.validation-rule"}, "pricing_rules": {"type": "component", "repeatable": true, "component": "calculator.pricing-rule"}, "conditional_logic": {"type": "component", "repeatable": true, "component": "calculator.conditional-logic"}, "default_value": {"type": "string"}, "placeholder": {"type": "string"}, "order": {"type": "integer", "default": 0}, "is_active": {"type": "boolean", "default": true}}}